#!/usr/bin/env python3
"""
修复EF5观测数据文件中的时间格式
将 "01-01-2009 08:00:00" 格式转换为 "01-01-2009" 格式
"""

import re
import sys

def fix_time_format(input_file, output_file):
    """修复时间格式"""
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        fixed_lines = []
        for i, line in enumerate(lines):
            if i == 0:  # 保持标题行不变
                fixed_lines.append(line)
                continue
            
            # 使用正则表达式匹配时间格式并替换
            # 匹配格式: DD-DD-YYYY HH:MM:SS,value
            pattern = r'(\d{2}-\d{2}-\d{4})\s+\d{2}:\d{2}:\d{2},(.*)'
            match = re.match(pattern, line.strip())
            
            if match:
                date_part = match.group(1)
                value_part = match.group(2)
                fixed_line = f"{date_part},{value_part}\n"
                fixed_lines.append(fixed_line)
            else:
                # 如果不匹配，保持原样
                fixed_lines.append(line)
        
        # 写入修复后的文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(fixed_lines)
        
        print(f"成功修复时间格式，处理了 {len(lines)} 行数据")
        print(f"输出文件: {output_file}")
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return False
    
    return True

if __name__ == "__main__":
    input_file = "examples/examples/mengjiang/obs/taiping.csv"
    output_file = "examples/examples/mengjiang/obs/taiping_fixed.csv"
    
    if fix_time_format(input_file, output_file):
        print("时间格式修复完成！")
        print("请将修复后的文件重命名为原文件名")
    else:
        print("时间格式修复失败！")
